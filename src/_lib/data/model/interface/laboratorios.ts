
import { ApiModelCollectionBuilder, ApiModelIndexBuilder, ApiModelCreateBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "@/_lib/config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { laboratoriosSchemas } from "../schema/laboratorios";

const { laboratorios } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(laboratorios) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(laboratorios) as (keyof typeof laboratoriosSchemas)[];

// Modelos generados
export const indexLaboratoriosModel = new ApiModelIndexBuilder(ACTION_NAMES[0])
  .setResponse(laboratoriosSchemas.index.response)
  .build();

export const collectionLaboratoriosModel = new ApiModelCollectionBuilder(ACTION_NAMES[1])
  .setFilters(laboratoriosSchemas.collection.filters)
  .setResponseItemsSchema(laboratoriosSchemas.collection.response)
  .build();

export const createLaboratoriosModel = new ApiModelCreateBuilder(ACTION_NAMES[2])
  .setBody(laboratoriosSchemas.create.body)
  .setResponse(laboratoriosSchemas.create.response)
  .build();
