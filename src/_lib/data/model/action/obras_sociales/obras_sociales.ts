'use server';

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, IndexHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { ObrasSocialesInterface } from "@/_lib/data/model/interface/";
import { getEntityConfig } from "@/_lib/utils/entities";

import { ServerActionParams } from "../actionFactory";

export async function submitObrasSocialesIndex() {
  "use server";

  const { meta } = getEntityConfig("obras_sociales", "index");

  const builder = new IndexHttpRequestBuilder(meta.url)
    .withValidation(ObrasSocialesInterface.indexObrasSocialesModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        ObrasSocialesInterface.indexObrasSocialesModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitObrasSocialesCollection( { query = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("obras_sociales", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .setQueryParams(query)
    .withValidation(ObrasSocialesInterface.collectionObrasSocialesModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        ObrasSocialesInterface.collectionObrasSocialesModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}
