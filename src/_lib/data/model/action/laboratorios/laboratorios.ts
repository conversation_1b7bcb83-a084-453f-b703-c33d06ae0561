"use server";

import { redirect } from "next/navigation";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { CollectionHttpRequestBuilder, IndexHttpRequestBuilder, CreateHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { LaboratoriosInterface } from "@/_lib/data/model/interface/";
import { getEntityConfig } from "@/_lib/utils/entities";

import { ServerActionParams } from "../actionFactory";
import { BadRequestError } from "@/_core/lib/context/error";

export async function submitLaboratoriosIndex() {
  "use server";

  const { meta } = getEntityConfig("laboratorios", "index");
  const builder = new IndexHttpRequestBuilder(meta.url)
    .withValidation(LaboratoriosInterface.indexLaboratoriosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        LaboratoriosInterface.indexLaboratoriosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitLaboratoriosCollection( { query = {} }: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("laboratorios", "collection");

  const builder = new CollectionHttpRequestBuilder(meta.url)
    .setQueryParams(query)
    .withValidation(LaboratoriosInterface.collectionLaboratoriosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        LaboratoriosInterface.collectionLaboratoriosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}

export async function submitLaboratoriosCreate( params: ServerActionParams ) {
  "use server";

  const { meta } = getEntityConfig("laboratorios", "create");

  const { body } = params;
  if (!body) {
    throw new BadRequestError( "La petición no contiene un body.");
  }


  const builder = new CreateHttpRequestBuilder(meta.url)
    .setBody(body)
    .withValidation(LaboratoriosInterface.createLaboratoriosModel)
    .addAfterExecute((response) => {
      const result = validateApiInput(
        LaboratoriosInterface.createLaboratoriosModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(() => {
      redirect("/laboratorios");
    });

  const response = await builder.run();
  return response.data;
}
