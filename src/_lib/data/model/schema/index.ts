import { z, ZodTypeAny } from "zod";

// Importar todos los módulos de esquemas
import { tokenSchemas } from "./token";
import { asociadosSchemas } from "./asociados";
import { localidadesSchemas } from "./localidades";
import { laboratoriosSchemas } from "./laboratorios";
import { obrasSocialesSchemas } from "./obras_sociales";
import { estadosLaboratoriosSchemas } from "./estados_laboratorios";

import { ENTITIES } from "@/_lib/config/entities";

// Mapeo de entidad → módulo de esquemas
const schemaModules = {
  token: tokenSchemas,
  asociados: asociadosSchemas,
  localidades: localidadesSchemas,
  laboratorios: laboratoriosSchemas,
  obras_sociales: obrasSocialesSchemas,
  estados_laboratorios: estadosLaboratoriosSchemas,
};

// Tipos de clave válidos
export type SchemaType = "body" | "query" | "params" | "headers" | "response";

// Tipo de una acción: cada una puede tener uno o más Zod schemas (body, response, etc.)
type ActionSchema = Partial<Record<SchemaType, ZodTypeAny>>;
type EntitySchema = Record<string, ActionSchema>;

// Generación automática del objeto `schemas`
export const schemas: Record<string, EntitySchema> = Object.entries(schemaModules).reduce(
  (acc, [entityKey, actions]) => {
    const actionSchemas: EntitySchema = Object.entries(actions).reduce(
      (actionAcc, [actionName, schemaMap]) => {
        actionAcc[actionName] = schemaMap as ActionSchema;
        return actionAcc;
      },
      {} as EntitySchema
    );
    acc[entityKey] = actionSchemas;
    return acc;
  },
  {} as Record<string, EntitySchema>
);

// ---------------------------
// Tipado utilitario
// ---------------------------

// Nivel 1: entidades
export type EntityKeys = keyof typeof ENTITIES;

// Nivel 2: acciones dentro de una entidad
export type ActionKeys<E extends EntityKeys> = keyof (typeof schemas)[E];

// Nivel 3: tipos de esquema dentro de una acción
export type SchemaTypeKeys<
  E extends EntityKeys,
  A extends ActionKeys<E>
> = Extract<keyof (typeof schemas)[E][A], SchemaType>;

// Inferencia automática de tipos de esquema
export type InferSchema<
  E extends EntityKeys,
  A extends ActionKeys<E>,
  S extends SchemaTypeKeys<E, A> = "body"
> = (typeof schemas)[E][A][S] extends ZodTypeAny
  ? z.infer<(typeof schemas)[E][A][S]>
  : never;
