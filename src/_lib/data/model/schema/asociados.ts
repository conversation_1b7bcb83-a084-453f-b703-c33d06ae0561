import { z } from "zod";
import { ENTITIES } from "@/_lib/config/entities";

export const ENTITY = ENTITIES.asociados;

export const asociadosSchemas = {
  index: {
    response: z.object({
      id: z.string().uuid(),
    }),
  },
  collection: {
    filters: z.object({
      page: z.number().int().min(1).default(1).optional().nullable(),
      limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
    }),
    response: z.object({
      id: z.number().int(),
      nombre: z.string(),
      apellido: z.string(),
      matriculas: z.array(
        z.object({
          numero: z.number().int(),
        })
      ).optional(),
      domicilio: z.object({
        calle: z.string(),
        numero: z.string(),
        localidad: z.object({
          nombre: z.string(),
        }),
      }).optional(),
      habilitaciones: z.array(
        z.object({
        })
      ).optional(),
    }),
  },
  create: {
    body: z.object({
      nombre: z
        .string({ errorMap: () => ({ message: "El nombre es obligatorio" }) })
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
      apellido: z
        .string({ errorMap: () => ({ message: "El apellido es obligatorio" }) })
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
      fechaNacimiento: z
        .string({
          errorMap: () => ({
            message: "La fecha de nacimiento es obligatoria",
          }),
        })
        .date("La fecha de nacimiento debe ser una fecha válida"),
      dni: z
        .number({
          errorMap: () => ({ message: "El DNI es obligatorio" }),
        })
        .int("El DNI debe ser un número entero").min(1000000, "El DNI debe tener al menos 7 dígitos").max(99999999, "El DNI debe tener como máximo 8 dígitos"),
      domicilio: z.object({
        fechaDesde: z.string().date("La fecha debe ser una fecha válida"),
        activo: z.boolean({
          errorMap: () => ({ message: "El domicilio es obligatorio" }),
        }).default(true),
        calle: z
          .string({
            errorMap: () => ({ message: "La calle es obligatoria" }),
          })
          .nonempty(),
        numero: z
          .string({
            errorMap: () => ({ message: "El número es obligatorio" }),
          })
          .nonempty(),
        piso: z.string().optional().nullable(),
        depto: z.string().optional().nullable(),
        descripcion: z.string().optional().nullable(),
        telefonoCodigoArea: z
          .string({
            errorMap: () => ({ message: "El código de área es obligatorio" }),
          })
          .regex(
            /^\d{2,4}$/,
            "El código de área debe contener entre 2 y 4 dígitos"
          ),
        telefonoNumero: z
          .string({
            errorMap: () => ({
              message: "El número de teléfono es obligatorio",
            }),
          })
          .regex(
            /^\d{6,8}$/,
            "El número de teléfono debe contener entre 6 y 8 dígitos"
          ),
        email: z.string().email("El email debe ser un email válido"),
        localidadId: z
          .number({
            errorMap: () => ({ message: "La localidad es obligatoria" }),
          })
          .int("El id de la localidad debe ser un número entero"),
      }),
      matricula: z.object({
        numero: z
          .number({
            errorMap: () => ({
              message: "El número de matrícula es obligatorio",
            }),
          })
          .int(),
        fechaAlta: z
          .string({
            errorMap: () => ({
              message: "La fecha de alta es obligatoria",
            }),
          })
          .date("La fecha de alta debe ser una fecha válida"),
        fechaEmisionTitulo: z
          .string({
            errorMap: () => ({
              message: "La fecha de emisión del título es obligatoria",
            }),
          })
          .date("La fecha de emisión del título debe ser una fecha válida"),
        fechaVencimiento: z
          .string({
            errorMap: () => ({
              message: "La fecha de vencimiento es obligatoria",
            }),
          })
          .date("La fecha de vencimiento debe ser una fecha válida"),
      }),
      habilitaciones: z.array(
        z.object({
          fechaDesde: z.string().date("La fecha debe ser una fecha válida"),
          fechaVencimiento: z.string().date("La fecha debe ser una fecha válida").optional().nullable(),
          codigoExterno: z.string().optional().nullable(),
          tramiteExternoNumero: z.string().optional().nullable(),
          observacion: z.string().optional().nullable(),
          tipoId: z.number().int(),
        })
      ).default([]),
    }),
    response: z.object({
      id: z.number().int(),
    }),
  },
} as const;
