import { EntityActions } from "../types";

export const OBRAS_SOCIALES_CONFIG: EntityActions = {
  collection: {
    description: "Listado de obras sociales",
    meta: {
      url: "/obras_sociales",
      method: "GET",
      revalidates: {
        keys: [],
      },
    },
    permissions: ["public"],
  },
  index: {
    description: "Detalle de obra social",
    meta: {
      url: "/obras_sociales/:id",
      method: "GET",
      revalidates: {
        keys: [],
      },
    },
    permissions: ["public"],
  },
} as const;
