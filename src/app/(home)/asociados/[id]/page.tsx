"use client";
import ReadIndexRenderer from "@/_core/ui/components/CRUD/readIndex/readIndexRenderer";
import EstadoUpdaterButton from "@/_core/ui/components/button/estadoUpdaterButton";
import HistoricalAccordion from "@/_core/ui/components/acordion/historicalAcordion";
import { Breadcrumbs, Stack, Typography } from "@mui/material";
import Link from "next/link";
import EditableTab from "@/_core/ui/components/tab/editableTab";
import ReadCollectionRenderer from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";

import { renderers } from "@/_core/ui/components/CRUD/readCollection/renderers";

export default function AsociadoPage() {
  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link className="hover:underline" href="/dashboard" color="inherit">
          Home
        </Link>
        <Link className="hover:underline" href="/asociados" color="inherit">
          Asociados
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">
          Juan Luis Hernandez
        </Typography>
      </Breadcrumbs>

      {/* Detalle del asociado */}
      <ReadIndexRenderer
        title="Juan Luis Hernandez"
        subtitle="DNI: 15333457"
        badges={[
          {
            label: "Activo",
            bgColor: "#F2F4F8",
            textColor: "contrastText.main",
          },
        ]}
        actions={<EstadoUpdaterButton onEstadoUpdate={() => {}} />}
        tabs={[
          {
            label: "Datos personales",
            content: (
              <Stack direction="column" spacing={2}>
                <EditableTab>
                  <div className="text-sm space-y-2">
                    <Typography variant="body1" color="text.secondary">
                      <strong>NOMBRE Y APELLIDO:</strong> Juan Luis Hernandez
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>FECHA NACIMIENTO:</strong> dd/mm/aaaa
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>DNI:</strong> 15333457
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>CUIT:</strong> 27-15333457-5
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>FECHA DE ALTA:</strong> dd/mm/aaaa
                    </Typography>

                    <HistoricalAccordion
                      title="Historial"
                      items={[
                        { label: "dd/mm/aaaa", value: "BAJA" },
                        { label: "MOTIVO:", value: "Fallecimiento" },
                        { label: "dd/mm/aaaa", value: "ACTIVO" },
                      ]}
                    />
                  </div>
                </EditableTab>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Matrícula
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>MAT. PROVINCIAL</strong> 655
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>FECHA VTO:</strong> dd/mm/aaaa
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>CATEGORIA:</strong> A
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Título
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>FECHA TITULO:</strong> dd/mm/aaaa
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Seguro de mala praxis
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>VTO. SEGURO MALA PRAXIS:</strong> dd/mm/aaaa
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Superintendencia Salud
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>N ° INSC. SSSALUD:</strong> 256348
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>VTO. INSC. SSSALUD:</strong> dd/mm/aaaa
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Domicilio Particular
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CALLE:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>NÚMERO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>PISO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>DPTO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>LOCALIDAD:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Contacto
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>TELÉFONO:</strong>2235-5628 75625
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>E-MAIL:</strong><EMAIL>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
              </Stack>
            ),
          },
          {
            label: "Laboral",
            content: (
              <Stack direction="column" spacing={2}>
                <EditableTab>
                  <div className="text-sm space-y-2">
                    <Stack direction="row" spacing={2}>
                      <Typography variant="body1" color="text.secondary">
                        <strong>RAZÓN SOCIAL:</strong> 
                      </Typography>
                      <Link href="/laboratorios/1">
                        <Typography variant="body1" color="link.main">
                          Nombre Laboratorio
                        </Typography>
                      </Link>
                    </Stack>
                    <Typography variant="body1" color="text.secondary">
                      <strong>SUCURSAL:</strong>
                    </Typography>
                  </div>
                </EditableTab>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Domicilio
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CALLE:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>NÚMERO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>PISO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>DPTO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>LOCALIDAD:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Contacto
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>TELÉFONO:</strong>2235-5628 75625
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>E-MAIL:</strong><EMAIL>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
              </Stack>
            ),
          },
          {
            label: "Cuenta",
            content:(
              <Stack direction="column" spacing={2}>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="cuenta"
                  columnsConfig={[
                    { key: "fecha_pago", label: "Fecha pago", renderer: renderers.default },
                    { key: "concepto", label: "Concepto", renderer: renderers.default },
                    { key: "importe", label: "Importe", renderer: renderers.currency },
                    { key: "estado", label: "Estado", renderer: renderers.badge, options: {
                      bg: "bg-gray-100",
                      text: "text-gray-700",
                    } },
                    { key: "__actions", label: "", renderer: renderers.actions },
                  ]}
                  variant="tab"
                />
              </Stack>
            ),
          },
          {
            label: "Configuraciones",
            content: (
              <Stack direction="column" spacing={2}>
                <Typography variant="h6xl" color="text.secondary">
                  Suscripciones
                </Typography>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="suscripciones"
                  columnsConfig={[
                    { key: "fecha_desde", label: "Fecha desde", renderer: renderers.default },
                    { key: "fecha_hasta", label: "Fecha hasta", renderer: renderers.default },
                    { key: "codigo", label: "Código", renderer: renderers.default },
                    { key: "retención", label: "Retención", renderer: renderers.currency },
                    { key: "valor", label: "Valor", renderer: renderers.currency },
                  ]}
                  variant="tab"
                />
              </Stack>
            ),
          },
          {
            label: "Trámites",
            content: (
              <Stack direction="column" spacing={2}>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="tramites"
                  columnsConfig={[
                    { key: "tramite", label: "Trámite", renderer: renderers.default },
                    { key: "numero", label: "Número", renderer: renderers.integer },
                    { key: "fecha", label: "Fecha", renderer: renderers.default },
                    { key: "fecha_vencimiento", label: "Fecha Vto.", renderer: renderers.default },
                  ]}
                  variant="tab"
                />
              </Stack>
            ),
          },
        ]}
      />
    </div>
  );
}
