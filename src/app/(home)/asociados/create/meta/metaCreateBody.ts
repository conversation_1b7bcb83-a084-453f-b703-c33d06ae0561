import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema } from "@/_lib/data/model/schema";
import { Step } from "@/_core/ui/components/forms/types";

export const metaCreateBody: Record<
  keyof InferSchema<"asociados", "create">,
  FieldMeta
> = {
  // Campos directos del nivel raíz
  apellido: {
    label: "Apellido",
    type: "text",
    grid: { xs: 12, md: 6, lg: 6, xl: 6 },
  },
  nombre: {
    label: "Nombre",
    type: "text",
    grid: { xs: 12, md: 6, lg: 6, xl: 6 },
  },
  fechaNacimiento: {
    label: "Fecha de nacimiento",
    type: "date",
    grid: { xs: 12, md: 6, lg: 6, xl: 6 },
  },
  dni: {
    label: "DNI",
    type: "integer",
    grid: { xs: 12, md: 6, lg: 6, xl: 6 },
  },

  // Objeto domicilio
  domicilio: {
    type: "group",
    label: "Domicilio",
    fields: {
      calle: {
        label: "Calle",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      numero: {
        label: "Número",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      piso: {
        label: "Piso (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      depto: {
        label: "Dpto (opcional)",
        type: "text",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      descripcion: {
        label: "Descripción (opcional)",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      localidadId: {
        label: "Localidad",
        type: "asyncSelect",
        entity: "localidades",
        labelKeys: ["codigoPostal", "nombre", "partido.nombre"],
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
      
      // Contacto
      telefonoCodigoArea: {
        label: "Código de área",
        type: "text",
        grid: { xs: 3, md: 3, lg: 3, xl: 3 },
      },
      telefonoNumero: {
        label: "Número de teléfono",
        type: "text",
        grid: { xs: 9, md: 9, lg: 9, xl: 9 },
      },
      email: {
        label: "Email",
        type: "text",
        grid: { xs: 12, md: 12, lg: 12, xl: 12 },
      },
    },
  },

  // Objeto matrícula
  matricula: {
    type: "group",
    label: "Matrícula",
    fields: {
      numero: {
        label: "Número de matrícula",
        type: "integer",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      fechaEmisionTitulo: {
        label: "Fecha de emisión del título",
        type: "date",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
      fechaVencimiento: {
        label: "Fecha de vencimiento",
        type: "date",
        grid: { xs: 12, md: 6, lg: 6, xl: 6 },
      },
    },
  },
};

export const stepsBody: Step<"asociados", "create">[] = [
  {
    label: "Datos personales",
    fields: [
      "apellido",
      "nombre",
      "fechaNacimiento",
      "dni",
      "domicilio",
    ],
  },
  {
    label: "Matrícula/Título",
    fields: [
      "matricula",
    ],
  },
];
