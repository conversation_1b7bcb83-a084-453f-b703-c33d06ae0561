'use server';

import Link from "next/link";
import { Breadcrumbs, Typography } from "@mui/material";
import { PermissionGuard } from "@/_core/ui/components/permissonGuard/permissionGuard";
import { PostCreateRenderer } from "@/_core/ui/components/CRUD/postCreate/postCreateRenderer";
import { InferSchema } from "@/_lib/data/model/schema";

import { metaCreateBody } from "./meta/metaCreateBody";

const ENTITY = "laboratorios";
const ACTION = "create";

export default async function Dashboard() {
  // Valores por defecto para campos requeridos
  const defaultValues = {
    // Agregar valores por defecto
  } as Partial<InferSchema<typeof ENTITY, typeof ACTION>>;

  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          className="hover:underline"
          href="/dashboard"
          color="inherit"
        >
          Home
        </Link>
        <Link
          className="hover:underline"
          href="/laboratorios"
          color="inherit"
        >
          Laboratorios
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">Nuevo</Typography>
      </Breadcrumbs>
      {/* Formulario */}
      <PermissionGuard entity="laboratorios" action="create" fallback={<></>}>
        <PostCreateRenderer
          entityName="laboratorios"
          actionName="create"
          meta={metaCreateBody}
          defaultValues={defaultValues}
          label="Nuevo Laboratorio"
        />
      </PermissionGuard>
    </div>
  );
}
