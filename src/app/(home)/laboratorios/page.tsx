"use client";

import React, { Suspense } from "react";
import ReadCollectionRenderer, { ColumnConfigWithStringRenderer } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { Typography, Stack } from "@mui/material";
import CreateButton from "@/_core/ui/components/button/createButton";

const ENTITY = "laboratorios";
const ACTION = "collection";



const columnsConfig: ColumnConfigWithStringRenderer[] = [
  {
    key: "razon_social",
    label: "RAZON SOCIAL",
    renderer: "navigate",
    options: {
      basePath: "/laboratorios",
      paramKey: "id",
    },
  },
  { key: "codigo", label: "CODIGO", renderer: "default" },
  { key: "cuit", label: "CUIT", renderer: "default" },
  { key: "iva", label: "IVA", renderer: "default" },
  { key: "localidad", label: "LOCALIDAD", renderer: "default" },
];

export default function LaboratoriosPage() {
  return (
    <main>
      <Stack direction="row" justifyContent="space-between">
        <Typography
          sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
          className="pl-4"
          color="text.primary"
        >
          Laboratorios
        </Typography>
        <CreateButton entityName={ENTITY} />
      </Stack>
      <Suspense fallback={<div>Cargando...</div>}>
        <ReadCollectionRenderer
          entityName={ENTITY}
          actionName={ACTION}
          columnsConfig={columnsConfig}
        />
      </Suspense>
    </main>
  );
}
